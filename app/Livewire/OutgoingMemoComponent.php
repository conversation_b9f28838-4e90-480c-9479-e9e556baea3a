<?php

namespace App\Livewire;

use App\Models\OutgoingMemo;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class OutgoingMemoComponent extends Component
{
    use WithPagination;

    // Search and filter properties
    public $search = '';
    public $fromDate = '';
    public $toDate = '';
    public $recipient = '';

    // Modal properties
    public $showModal = false;
    public $editMode = false;
    public $memoId = null;

    // Form properties
    public $date_of_dispatch = '';
    public $registry_no = '';
    public $recipient_field = '';
    public $date_of_letter = '';
    public $letter_no = '';
    public $subject = '';
    public $remark = '';

    // Confirmation modal
    public $confirmingDeletion = false;
    public $memoToDelete = null;

    protected $rules = [
        'date_of_dispatch' => 'required|date',
        'registry_no' => 'required|numeric',
        'recipient_field' => 'required|string|max:100',
        'date_of_letter' => 'required|date',
        'letter_no' => 'required|numeric',
        'subject' => 'required|string',
        'remark' => 'nullable|string|max:255',
    ];

    protected $messages = [
        'date_of_dispatch.required' => 'Date of dispatch is required.',
        'registry_no.required' => 'Registry number is required.',
        'registry_no.numeric' => 'Registry number must be a number.',
        'recipient_field.required' => 'Recipient is required.',
        'date_of_letter.required' => 'Date of letter is required.',
        'letter_no.required' => 'Letter number is required.',
        'letter_no.numeric' => 'Letter number must be a number.',
        'subject.required' => 'Subject is required.',
    ];

    public function mount()
    {
        $this->date_of_dispatch = now()->format('Y-m-d');
        $this->date_of_letter = now()->format('Y-m-d');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFromDate()
    {
        $this->resetPage();
    }

    public function updatingToDate()
    {
        $this->resetPage();
    }

    public function updatingRecipient()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->fromDate = '';
        $this->toDate = '';
        $this->recipient = '';
        $this->resetPage();
    }

    public function openModal()
    {
        try {
            $this->resetForm();
            $this->editMode = false;
            $this->showModal = true;

            // Debug: Log that the method was called
            Log::info('OutgoingMemo openModal called, showModal is now: ' . ($this->showModal ? 'true' : 'false'));

            // Dispatch browser event for additional debugging
            $this->dispatch('modal-opened', ['component' => 'outgoing-memo']);
        } catch (\Exception $e) {
            Log::error('Error in OutgoingMemo openModal: ' . $e->getMessage());
            flash()->error('Error opening modal: ' . $e->getMessage());
        }
    }

    public function editMemo($id)
    {
        $memo = OutgoingMemo::findOrFail($id);
        
        $this->memoId = $memo->id;
        $this->date_of_dispatch = $memo->date_of_dispatch->format('Y-m-d');
        $this->registry_no = $memo->registry_no;
        $this->recipient_field = $memo->recipient;
        $this->date_of_letter = $memo->date_of_letter->format('Y-m-d');
        $this->letter_no = $memo->letter_no;
        $this->subject = $memo->subject;
        $this->remark = $memo->remark;
        
        $this->editMode = true;
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->memoId = null;
        $this->date_of_dispatch = now()->format('Y-m-d');
        $this->registry_no = '';
        $this->recipient_field = '';
        $this->date_of_letter = now()->format('Y-m-d');
        $this->letter_no = '';
        $this->subject = '';
        $this->remark = '';
        $this->resetErrorBag();
    }

    public function save()
    {
        $this->validate();

        try {
            $data = [
                'date_of_dispatch' => $this->date_of_dispatch,
                'registry_no' => $this->registry_no,
                'recipient' => $this->recipient_field,
                'date_of_letter' => $this->date_of_letter,
                'letter_no' => $this->letter_no,
                'subject' => $this->subject,
                'remark' => $this->remark,
                'created_by' => Auth::id(),
            ];

            if ($this->editMode) {
                $memo = OutgoingMemo::findOrFail($this->memoId);
                $memo->update($data);
                flash()->success('Outgoing memo updated successfully!');
            } else {
                OutgoingMemo::create($data);
                flash()->success('Outgoing memo created successfully!');
            }

            $this->closeModal();
        } catch (\Exception $e) {
            flash()->error('An error occurred while saving the memo.');
        }
    }

    public function confirmDelete($id)
    {
        $this->memoToDelete = $id;
        $this->confirmingDeletion = true;
    }

    public function cancelDelete()
    {
        $this->confirmingDeletion = false;
        $this->memoToDelete = null;
    }

    public function deleteMemo()
    {
        try {
            if ($this->memoToDelete) {
                OutgoingMemo::findOrFail($this->memoToDelete)->delete();
                flash()->success('Outgoing memo deleted successfully!');
            }
        } catch (\Exception $e) {
            flash()->error('An error occurred while deleting the memo.');
        } finally {
            $this->cancelDelete();
        }
    }

    public function render()
    {
        $memos = OutgoingMemo::with('creator')
            ->search($this->search)
            ->dateRange($this->fromDate, $this->toDate)
            ->toRecipient($this->recipient)
            ->latest('date_of_dispatch')
            ->paginate(10);

        return view('livewire.outgoing-memo-component', compact('memos'));
    }
}
